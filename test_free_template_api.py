#!/usr/bin/env python3
"""
测试免费简历模板API接口
"""
import requests
import json
from urllib.parse import quote

# API基础URL
BASE_URL = "http://localhost:18080"

def test_get_all_templates():
    """测试获取所有模板列表"""
    print("=== 测试获取所有模板列表 ===")
    url = f"{BASE_URL}/free-templates/"

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"总模板数: {data['total']}")
            print(f"返回模板数: {len(data['templates'])}")

            for template in data['templates']:
                print(f"- {template['id']} ({template['type']})")
                print(f"  缩略图URL: {template['thumb_url']}")
                print(f"  百度网盘: {template.get('baidu_url', 'N/A')}")
                print(f"  夸克网盘: {template.get('quark_url', 'N/A')}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    print()

def test_get_batches():
    """测试获取批次列表"""
    print("=== 测试获取批次列表 ===")
    url = f"{BASE_URL}/free-templates/batches"

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            batches = response.json()
            print(f"批次列表: {batches}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    print()

def test_get_templates_by_batch():
    """测试根据批次获取模板"""
    print("=== 测试根据批次获取模板 ===")
    batch_flag = "blackWhite"
    url = f"{BASE_URL}/free-templates/batch/{batch_flag}"

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"批次: {data['batch_flag']}")
            print(f"该批次模板数: {data['total']}")

            for template in data['templates']:
                print(f"- {template['id']} ({template['type']})")
                print(f"  缩略图URL: {template['thumb_url']}")
                print(f"  百度网盘: {template.get('baidu_url', 'N/A')}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    print()

def test_get_download_links():
    """测试获取下载链接"""
    print("=== 测试获取下载链接 ===")
    template_id = "blackWhite/10.jpg"
    # URL编码模板ID
    encoded_id = quote(template_id, safe='')
    url = f"{BASE_URL}/free-templates/{encoded_id}/download"

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")
        print(f"请求URL: {url}")

        if response.status_code == 200:
            data = response.json()
            print(f"模板ID: {data['id']}")
            print(f"百度网盘: {data['baidu_url']}")
            print(f"百度提取码: {data['baidu_pass']}")
            print(f"夸克网盘: {data['quark_url']}")
            print(f"夸克提取码: {data['quark_pass']}")
            print(f"下载次数: {data['download_count']}")
            print(f"消息: {data['message']}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    print()

def test_get_template_detail():
    """测试获取模板详情"""
    print("=== 测试获取模板详情 ===")
    template_id = "blackWhite/1.jpg"
    encoded_id = quote(template_id, safe='')
    url = f"{BASE_URL}/free-templates/{encoded_id}"

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"模板详情:")
            print(f"- ID: {data['id']}")
            print(f"- 缩略图URL: {data['thumb_url']}")
            print(f"- 文件类型: {data['type']}")
            print(f"- 百度网盘: {data.get('baidu_url', 'N/A')}")
            print(f"- 百度提取码: {data.get('baidu_pass', 'N/A')}")
            print(f"- 夸克网盘: {data.get('quark_url', 'N/A')}")
            print(f"- 夸克提取码: {data.get('quark_pass', 'N/A')}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    print()

def test_get_stats():
    """测试获取统计信息"""
    print("=== 测试获取统计信息 ===")
    url = f"{BASE_URL}/free-templates/stats/summary"

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"统计信息:")
            print(f"- 总模板数: {data['total_templates']}")
            print(f"- 总下载次数: {data['total_downloads']}")

            print(f"- 按批次统计:")
            for batch_stat in data['batch_stats']:
                print(f"  * {batch_stat['batch_flag']}: {batch_stat['template_count']} 个模板, {batch_stat['download_count']} 次下载")

            print(f"- 按类型统计:")
            for type_stat in data['type_stats']:
                print(f"  * {type_stat['type']}: {type_stat['count']} 个模板")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {str(e)}")
    print()

def main():
    """主测试函数"""
    print("开始测试免费简历模板API接口...")
    print()

    # 测试各个接口
    test_get_all_templates()
    test_get_batches()
    test_get_templates_by_batch()
    test_get_download_links()
    test_get_template_detail()
    test_get_stats()

    print("API接口测试完成！")

if __name__ == "__main__":
    main()
