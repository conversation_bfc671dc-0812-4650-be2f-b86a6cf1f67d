<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>张展伟的简历</title>
<link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
<style>
:root {
--theme-color: #2B6CB0;
--base-font-size: 11pt;
--font-size: var(--base-font-size);
--spacing: 1.5;
--text-color: #333333;
--secondary-color: #666666;
}

@page {
size: A4;
margin: 0;
}

body {
margin: 0;
padding: 0;
font-family: "Source Han Sans SC VF", sans-serif;
}


.resumeTemplateA01 {
width: 100%;
height: 100%;
margin: 0;
padding: 0;
display: flex;
flex-direction: column;
}

/* 添加打印媒体查询和分页控制 */
@media print {
.resumeTemplateA01 {
    overflow: hidden;
    margin: 0;
    padding: 10mm;
}

.section {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 8mm;
    position: relative;
}

.title {
    break-after: avoid;
    page-break-after: avoid;
}

.edu-item,
.work-item,
.project-item,
.internship-item,
.info-wrapper,
.custom-header,
.school-item {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 4mm;
}

.description {
    orphans: 3;
    widows: 3;
}

.photo-wrapper,
.photo,
img {
    break-inside: avoid;
    page-break-inside: avoid;
}

.content {
    break-before: avoid;
    page-break-before: avoid;
}

table, ul, ol {
    break-inside: avoid;
    page-break-inside: avoid;
}
}

.resumeTemplateA01 {
position: relative;
width: 210mm;
/* min-height: 297mm; */
margin: 0 auto;
padding: 10mm;
box-sizing: border-box;
font-size: var(--font-size);
line-height: var(--spacing);
color: var(--text-color);
page-break-after: always;
}

.resume-decoration-line {
content: '';
position: absolute;
left: 13mm;
top: calc(var(--base-font-size) + 2pt + 25mm);
bottom: 10mm;
width: 0.05mm;
background-color: var(--theme-color);
opacity: 0.8;
z-index: 1;
}

.resume-header {
margin-bottom: 5mm;
display: flex;
align-items: center;
margin-top: 2mm;
}

.icons-group {
display: flex;
gap: 3mm;
margin-left: 110mm;
margin-top: -21pt;
}

.resume-title {
font-size: 30pt;
font-weight: bold;
color: var(--theme-color);
margin-top: -25pt;
margin-bottom: 0pt;
margin-left: -2mm;
margin-right: 0pt;
}

.icon-circle {
width: 10mm;
height: 10mm;
border-radius: 50%;
background-color: var(--theme-color);
display: flex;
align-items: center;
justify-content: center;
}

.icon-circle svg {
width: 5mm;
height: 5mm;
}

.decoration-line {
width: 104%;
margin: -10mm -10mm 15mm -10mm;
padding: 0 10mm;
text-align: left;
margin-left: -23mm;
margin-top: -3mm;
}

.decoration-line svg {
width: 107%;
height: 10mm;
}

.section {
margin-bottom: 5mm;
padding: 3mm;
border-radius: 2mm;
margin-top: -10mm;
position: relative;
}

.section-basic-info {
margin-top: -18mm;
}

.basic-info-decoration {
width: 87%;
margin-top: -3.6mm;
margin-left: -12mm;
transform: scale(1.3);
transform-origin: left center;
color: var(--theme-color);
}

.basic-info-decoration svg {
width: 100%;
height: auto;
}

.title {
position: relative;
z-index: 2;
font-size: calc(var(--base-font-size) + 0pt);
line-height: 1;
color: #FFFFFF;
font-weight: bold;
padding: 2mm 0 2mm -2mm;
letter-spacing: 2pt;
}

.section-basic-info .title {
position: relative;
z-index: 2;
font-size: calc(var(--base-font-size) + 0pt);
line-height: 1;
color: #FFFFFF;
font-weight: bold;
padding: 2mm 0 2mm -2mm;
}

.content {
margin-bottom: 3mm;
margin-top: 3mm;
padding: 0 5mm;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color, #333333);
gap: 1mm;
display: flex;
flex-wrap: wrap;
letter-spacing: 1.2pt;
}

.degree, .time {
color: var(--secondary-color, #666666);
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
font-weight: bold;
}

/* .courses {
color: var(--text-color, #333333);
margin-top: 0.5mm;
font-size: calc(var(--base-font-size) - 2pt);
line-height: var(--spacing);
} */

.description {
color: var(--text-color, #333333);
margin-top: 0.5mm;
white-space: pre-wrap;
word-break: break-all;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
width: 100%;
}

.info-item {
margin-bottom: 1.5mm;
line-height: var(--spacing);
color: var(--text-color);
}

.info-item span {
font-size: calc(var(--base-font-size));
}

.info-grid {
display: grid;
grid-template-columns: 60% 1fr;
gap: 2.5mm;
width: 100%;
}

.edu-item, .work-item, .internship-item, .project-item, .school-item {
margin-bottom: 3mm;
width: 750px;
}

.time {
color: var(--text-color);
font-size: calc(var(--base-font-size) + 0pt);
margin-bottom: 1.5mm;
font-weight: bold;
}

.school, .company {
font-weight: bold;
margin-bottom: 1.5mm;
}

.major, .position {
color: var(--secondary-color);
margin-bottom: 1.5mm;
}

.info-wrapper {
display: flex;
justify-content: space-between;
align-items: flex-start;
width: 100%;
margin-bottom: 2.5mm;
}

.info-grid {
flex: 1;
display: grid;
grid-template-columns: 1fr 1fr;
gap: 2mm;
margin-right: 5mm;
}

.photo-wrapper {
flex-shrink: 0;
width: 35mm;
height: 45mm;
border: 0.35mm solid #ddd;
display: flex;
align-items: center;
justify-content: center;
background: #fff;
}

.photo {
max-width: 100%;
max-height: 100%;
object-fit: contain;
}

.info-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
}

.info-item .label {
display: inline-block;
min-width: 12mm;
vertical-align: top;
}

.info-item .value {
display: inline-block;
word-break: break-word;
overflow-wrap: break-word;
max-width: calc(100% - 15mm);
}

/* 求职意向样式 */
.job-intention-content {
display: flex;
flex-direction: column;
/* gap: 2mm; */
/* background-color: #ad5a5a; */
}

.job-intention-row {
/* display: flex;
gap: 3mm; */
display: grid;
grid-template-columns: 1fr 1fr;
gap: 3mm;
margin-bottom: 2mm;
/* background-color: #be2020; */
}

.job-intention-item {
flex: 1;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
/* line-height: 0.5; */
color: var(--text-color);
}

.job-intention-label {
font-weight: bold;
margin-right: 2mm;
}

.job-intention-value {
color: var(--text-color);
}

/* 在校经历样式 */
.school-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.school-role, .school-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.school-role {
text-align: left;
}

.school-date {
text-align: left;
}

.school-content {
color: var(--text-color);
margin-top: 0.5mm;
white-space: pre-wrap;
word-break: break-all;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
width: 100%;
}

/* 教育经历样式 */
.education-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
/* background-color: #be2020; */
}

.school, .major-degree, .edu-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.school {
text-align: left;
}

.major-degree {
text-align: left;
}

.edu-date {
text-align: left;
}

/* .courses-label {
font-weight: bold;
} */

/* 工作经历样式 */
.work-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.company, .position, .work-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.company {
text-align: left;
}

.position {
text-align: left;
}

.work-date {
text-align: left;
}

/* 实习经历样式 */
.internship-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.intern-company, .intern-position, .intern-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.intern-company {
text-align: left;
}

.intern-position {
text-align: left;
}

.intern-date {
text-align: left;
}

/* 项目经历样式 */
.project-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.project-name, .project-role, .project-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.project-name {
text-align: left;
}

.project-role {
text-align: left;
}

.project-date {
text-align: left;
}

/* 技能和获奖样式 */
.three-column {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}


.skill-item, .award-item, .interest-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
}

/* 自定义模块样式 */
.custom-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 3mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.custom-name, .time {
font-weight: bold;
font-size: calc(var(--base-font-size) + 0pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.custom-name {
text-align: left;
}

.time {
text-align: left;
}

/* 隐藏空模块 */
.hidden {
display: none;
}
</style>
</head>

<body>

<div class="resumeTemplateA01">
<div class="resume-decoration-line"></div>
<!-- 主标题 -->
<div class="resume-header">
    <div class="resume-title">个人简历</div>
    <div class="icons-group">
        <div class="icon-circle">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M946.2 553.66H766.9l0.35 73.56h-63.78l-0.35-73.56h-382l0.35 73.56h-63.78l-0.35-73.56H65v281.23a59.79 59.79 0 0 0 59.79 59.79h774.42A59.79 59.79 0 0 0 959 834.89V553.7q-6.4-0.03-12.8-0.04zM77.8 502.12h179.62l0.22-17.4a16 16 0 0 1 16-15.79h31.79a16 16 0 0 1 16 16.2l-0.21 17h381.83l0.22-17.4a16 16 0 0 1 16-15.79h31.78a16 16 0 0 1 16 16.2l-0.22 17h192V352.55a64 64 0 0 0-63.78-63.78H767.54V193.1a64 64 0 0 0-63.78-63.78H320.24a64 64 0 0 0-63.78 63.78v95.67H128.9a64 64 0 0 0-63.78 63.78v149.53q6.34 0.03 12.68 0.04z m243.27-308.91l0.11-0.11h381.64l0.11 0.11v95.56H321.07z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="icon-circle">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M466.090667 148.181333a86.485333 86.485333 0 0 1 78.848 0.768L951.466667 362.026667a17.28 17.28 0 0 1-0.298667 30.805333l-405.76 203.093333a86.485333 86.485333 0 0 1-78.890667-0.768L60.032 382.037333a17.28 17.28 0 0 1 0.256-30.805333l405.802667-203.050667z M211.2 502.314667v193.109333c0 6.570667 3.712 12.544 9.557333 15.488l266.154667 132.992c11.861333 5.973333 25.813333 6.101333 37.845333 0.426667l281.856-133.546667a17.28 17.28 0 0 0 9.898667-15.616v-191.786667l-310.784 155.392-294.528-156.458666z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="icon-circle">
            <svg viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M1018.319924 112.117535q4.093748 9.210934 6.652341 21.492179t2.558593 25.585928-5.117186 26.609365-16.374994 25.585928q-12.281245 12.281245-22.003898 21.492179t-16.886712 16.374994q-8.187497 8.187497-15.351557 14.32812l-191.382739-191.382739q12.281245-11.257808 29.167958-27.121083t28.144521-25.074209q14.32812-11.257808 29.679676-15.863275t30.191395-4.093748 28.656239 4.605467 24.050772 9.210934q21.492179 11.257808 47.589826 39.402329t40.425766 58.847634zM221.062416 611.554845q6.140623-6.140623 28.656239-29.167958t56.289041-56.80076l74.710909-74.710909 82.898406-82.898406 220.038979-220.038979 191.382739 192.406177-220.038979 220.038979-81.874969 82.898406q-40.937484 39.914047-73.687472 73.175753t-54.242167 54.753885-25.585928 24.562491q-10.234371 9.210934-23.539054 19.445305t-27.632802 16.374994q-14.32812 7.16406-41.960921 17.398431t-57.824197 19.957024-57.312478 16.886712-40.425766 9.210934q-27.632802 3.070311-36.843736-8.187497t-5.117186-37.867173q2.046874-14.32812 9.722653-41.449203t16.374994-56.289041 16.886712-53.730448 13.304682-33.773425q6.140623-14.32812 13.816401-26.097646t22.003898-26.097646z" fill="#ffffff"></path>
            </svg>
        </div>
    </div>
</div>
<!-- 装饰线 -->
<div class="decoration-line">
    <svg width="2000" height="25" xmlns="http://www.w3.org/2000/svg">
        <g>
            <rect stroke="#AAAAAA" id="svg_4" height="8" width="900" y="9.5" x="427" fill="#AAAAAA"/>
            <path stroke="var(--theme-color)" id="svg_5" d="m10,5l417,0l0,13l-417,0l0,-13z" fill="var(--theme-color)"/>
            <path stroke="var(--theme-color)" id="svg_6" d="m427,18l0,-13l11.4,13l-11.4,0z" fill="var(--theme-color)"/>
        </g>
    </svg>
</div>


<!-- 根据moduleOrders排序显示各个模块 -->

  <!-- 基本信息 -->
  
  <div class="section section-basic-info">
      <div class="title">基本信息</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          <div class="info-wrapper">
              <div class="info-grid">
                  <div class="info-item"><span class="label">姓名：</span><span class="value">张展伟</span></div>
                  <div class="info-item"><span class="label">性别：</span><span class="value">男</span></div>
                  <div class="info-item"><span class="label">年龄：</span><span class="value">30</span></div>
                  <div class="info-item"><span class="label">电话：</span><span class="value">18600009999</span></div>
                  <div class="info-item"><span class="label">邮箱：</span><span class="value"><EMAIL></span></div>
                  <div class="info-item"><span class="label">城市：</span><span class="value">上海</span></div>
                  
                  <div class="info-item"><span class="label">婚姻：</span><span class="value">未婚</span></div>
                  <div class="info-item"><span class="label">政治面貌：</span><span class="value">群众</span></div>
                  <div class="info-item"><span class="label">民族：</span><span class="value">汉</span></div>
                  <div class="info-item"><span class="label">籍贯：</span><span class="value">浙江</span></div>
                  <div class="info-item"><span class="label">身高：</span><span class="value">180</span></div>
                  <div class="info-item"><span class="label">体重：</span><span class="value">80</span></div>
                  <div class="info-item"><span class="label">微信：</span><span class="value">zhanweir</span></div>
              </div>
              
              <div class="photo-wrapper">
                  <img src="data:image/jpeg;base64,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" alt="证件照" class="photo" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22200%22%20viewBox%3D%220%200%20150%20200%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22150%22%20height%3D%22200%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2275%22%20y%3D%22100%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'" />
              </div>
              
          </div>
      </div>
  </div>
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  
  <div class="section">
      <div class="title">教育经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">上海交大</div>
                  <div class="major-degree">会计学 / 本科</div>
                  <div class="edu-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">统计学, 数学, 英语, 市场营销</div>
          </div>
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">上海复旦大学</div>
                  <div class="major-degree">新闻学 / 硕士</div>
                  <div class="edu-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">媒体传播, 组织学, 营销学</div>
          </div>
          
      </div>
  </div>
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  
  <div class="section">
      <div class="title">在校经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="school-item">
              <div class="school-header">
                  <div class="school-role">学生会会长</div>
                  <div class="empty-div"></div>
                  <div class="school-date">2025-06 - 2025-06</div>
              </div>
              <div class="school-content">在任职学生会会长期间, 我管理了1000人的社团, 组织外联, 喝酒聚会</div>
          </div>
          
          <div class="school-item">
              <div class="school-header">
                  <div class="school-role">篮球社队长</div>
                  <div class="empty-div"></div>
                  <div class="school-date">2025-06 - 2025-06</div>
              </div>
              <div class="school-content">我篮球打的特别好, 在胜利拿过奖, 可以帮领导一起大球</div>
          </div>
          
      </div>
  </div>
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  
  <div class="section">
      <div class="title">名称二</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="custom-header">
              <div class="custom-name">大幅</div>
              <div class="empty-div"></div>
              <div class="time">2025-06 - 至今</div>
          </div>
          <div class="description">习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</div>
          
      </div>
  </div>
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  
  <div class="section">
      <div class="title">技能特长</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          <div class="three-column">
              
              <div class="skill-item">技能1</div>
              
              <div class="skill-item">技能2</div>
              
              <div class="skill-item">技能3</div>
              
              <div class="skill-item">技能4</div>
              
              <div class="skill-item">技能5</div>
              
          </div>
      </div>
  </div>
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  
  <div class="section">
      <div class="title">自我评价</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="description">CSS align-items 属性设置了所有直接子元素的 align-self 值作为一个组。在 Flexbox 中，它控制子元素在交叉轴上的对齐。在 Grid 布局中，它控制了子元素在其网格区域内的块向轴上的对齐。

下面的交互示例演示了使用网格布局的 align-items 的一些值。</div>
          
      </div>
  </div>
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  
  <div class="section">
      <div class="title">规划</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="custom-header">
              <div class="custom-name">队长</div>
              <div class="empty-div"></div>
              <div class="time">2025-06 - 2025-06</div>
          </div>
          <div class="description">习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</div>
          
      </div>
  </div>
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  
  <div class="section">
      <div class="title">工作经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="work-item">
              <div class="work-header">
                  <div class="company">上海证券有限责任公司</div>
                  <div class="position">投资顾问</div>
                  <div class="work-date">2025-06 - 至今</div>
              </div>
              <div class="description">投资顾问分析, 基金分析, 上市公司调研
上市公司年报统计, 每周投资报告
组织部门活动, 高净值活动</div>
          </div>
          
          <div class="work-item">
              <div class="work-header">
                  <div class="company">上海宽辅私募基金合伙企业</div>
                  <div class="position">交易员职位</div>
                  <div class="work-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理
每天都有很多交易任务</div>
          </div>
          
      </div>
  </div>
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  
  <div class="section">
      <div class="title">项目经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="project-item">
              <div class="project-header">
                  <div class="project-name">ctp数据录取</div>
                  <div class="project-role">程序员</div>
                  <div class="project-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">期货账户编写c++程序录制实时行情, level1 高频行情, 可以为交易分析 做基础</div>
          </div>
          
          <div class="project-item">
              <div class="project-header">
                  <div class="project-name">多空融券约券系统</div>
                  <div class="project-role">程序员技术员</div>
                  <div class="project-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">根据也无需要, 对接券商空头约券系统, 实现实时约券查券
根据也无需要, 对接券商空头约券系统, 实现实时约券查券</div>
          </div>
          
      </div>
  </div>
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  
  <div class="section">
      <div class="title">获奖证书</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          <div class="three-column">
              
              <div class="award-item">奖项1</div>
              
              <div class="award-item">奖项2</div>
              
              <div class="award-item">奖项3</div>
              
              <div class="award-item">奖项4</div>
              
          </div>
      </div>
  </div>
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  
  <div class="section">
      <div class="title">求职意向</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content job-intention-content">
          <div class="job-intention-row">
              <div class="job-intention-item"><span class="job-intention-label">期望职位：</span><span class="job-intention-value">总裁</span></div>
              <div class="job-intention-item"><span class="job-intention-label">期望薪资：</span><span class="job-intention-value">3k以下</span></div>
          </div>
          <div class="job-intention-row">
              <div class="job-intention-item"><span class="job-intention-label">期望城市：</span><span class="job-intention-value">上海</span></div>
              <div class="job-intention-item"><span class="job-intention-label">求职状态：</span><span class="job-intention-value">目前在职</span></div>
          </div>
      </div>
  </div>
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  
  <div class="section">
      <div class="title">实习经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="internship-item">
              <div class="internship-header">
                  <div class="intern-company">阿里集团</div>
                  <div class="intern-position">秘书</div>
                  <div class="intern-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">帮领导订饭店, 打酱油
参加部门会议</div>
          </div>
          
          <div class="internship-item">
              <div class="internship-header">
                  <div class="intern-company">量化私募</div>
                  <div class="intern-position">IT</div>
                  <div class="intern-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">抓数据, 洗数据, 做项目
啦啦啦</div>
          </div>
          
      </div>
  </div>
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  
  <div class="section">
      <div class="title">人呢</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="custom-header">
              <div class="custom-name">角色</div>
              <div class="empty-div"></div>
              <div class="time">2025-06 - 2025-06</div>
          </div>
          <div class="description">习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</div>
          
      </div>
  </div>
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  


</div> <!-- 关闭 resumeTemplateA01 -->


</body>
</html>