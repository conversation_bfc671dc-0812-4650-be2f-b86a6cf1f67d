<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>张展伟的简历</title>
  <link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css" />
  <style>
    :root {
      --theme-color: #2E75B6;
      --base-font-size: 11pt;
      --max-font-size: 13pt;
      --spacing: 1.5;
      --text-color: #000000;
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      /* font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; */
      /* font-family: 'Alibaba PuHuiTi', 'Noto Sans SC'; */
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 0px 0;
      overflow-x: hidden;
      font-family: "Source Han Sans SC VF", sans-serif;
    }

    .resume-container {
      width: 210mm;
      /* min-height: 297mm; */
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
    }

    /* Header Section */
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--theme-color);
      padding: 15px 30px;
      color: white;
    }

    .resume-header-title {
      font-size: calc(var(--base-font-size) * 2);
      font-weight: bold;
      letter-spacing: 2px;
    }

    .resume-header-icons {
      display: flex;
      gap: 20px;
    }

    .resume-header-icons i {
      font-size: calc(var(--base-font-size) * 1.8);
      display: inline-block !important;
      width: auto !important;
      height: auto !important;
    }

    /* Basic Info Section */
    .basic-info-section {
      padding: 20px 30px;
      display: grid;
      grid-template-columns: 1fr auto; /* Main content | Photo */
      gap: 25px;
      /* border-bottom: 1px solid var(--border-color); */
      align-items: start;
    }

    .basic-info-left {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .basic-info-name h2 {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .basic-info-name h2 span {
        font-size: calc(var(--base-font-size) *1.8);
        font-weight: bold;
        color: #000000;
    }
    .basic-info-name h2::after {
      content: "";
      flex-grow: 1;
      height: 1px;
      margin-left: 10px;
      background-color: var(--theme-color);
    }

    .basic-info-details {
      display: grid;
      grid-template-columns: repeat(2, minmax(200px, 1fr)); /* Responsive columns */
      gap: 8px 15px; /* Row and column gap */
      font-size: var(--base-font-size);
      color: var(--secondary-text-color);
      /* background-color: rgb(255, 255, 255); */
    }

    .basic-info-details p {
      display: flex;
      align-items: baseline;
    }

    .basic-info-details p span:first-child {
      font-weight: bold;
      min-width: 100px; /* Adjusted for labels like "政治面貌" */
      text-align: justify;
      color: var(--text-color);
      /* text-align: center; remove this to align left */
      background-color: rgb(255, 255, 255);
      margin-right: 5px; /* Add some space between label and value */
    }
     .basic-info-details p span:last-child {
       word-break: break-all;
     }

    .basic-info-photo {
      width: 110px;
      height: 154px;
      object-fit: cover;
      border: 1px solid var(--border-color);
      align-self: center;
    }

    /* General Section Styling */
    .section {
      padding: 5px 25px;
      margin-bottom: 0px;
    }
    .section#name-header {
      padding: 15px 25px 0px 25px;
    }

    .section-header {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      background-color: rgb(255, 255, 255);
    }

    .section-header i {
      margin-right: 10px;
      width: 18px;
      font-size: calc(var(--base-font-size) * 1.1);
      color: #fff; /* Icon color inside title */
      display: inline-block; /* 确保图标正确显示 */
    }

    .section-title {
        display: flex;
        align-items: center;
        font-size: calc(var(--base-font-size) * 1.3);
        background-color: var(--theme-color);
        border-radius: 6px;
        padding: 6px 15px;
        color: #fff;
        margin-right:10px; /* Space between title and line */
    }

    /* 确保Font Awesome图标正确显示 */
    .section-title i {
        display: inline-block !important;
        width: auto !important;
        height: auto !important;
        margin-right: 8px;
    }
    .section-title h2 {
      font-weight: bold;
      font-size: calc(var(--base-font-size) * 1.3);
      color: #fff; /* Ensure h2 color is white */
    }

    .section-header-line { /* Renamed from ::after for clarity */
      flex-grow: 1;
      height: 1px;
      background-color: var(--theme-color);
    }

    .section-content {
      padding-left: 5px;
      background-color: rgb(255, 255, 255);
    }

    .section-item {
        margin-bottom: 5px;
    }
    .item-header {
        margin-bottom: 5px;
    }

    .section-item .three-column {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        font-weight: bold;
    }
    .section-item .two-column {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        font-weight: bold;
    }
    .item-header .date-range {
      color: var(--secondary-text-color);
      font-weight: normal;
      text-align: right; /* Align dates to the right */
    }

    .item-description p {
      color: var(--secondary-text-color);
      padding-left: 15px;
    }
    .item-description p strong {
        color: var(--text-color);
        margin-right: 5px;
    }

    .horizon-item-list {
        padding-left: 20px;
        display: flex;
        align-items: center;
        flex-wrap:wrap;
        gap: 25px; /* Reduced gap */
    }
    .horizon-item-list li {
        margin-bottom: 5px; /* Add some bottom margin for wrapped items */
        background-color: #f0f0f0; /* Light background for items */
        padding: 3px 8px;
        border-radius: 4px;
        font-size: calc(var(--base-font-size) * 0.95);
    }
     .long-text-item {
        margin-bottom: 10px;
        line-height: var(--spacing);
     }
     .long-text-item .date-prefix {
         font-weight: bold;
         margin-right: 8px;
         color: var(--text-color);
     }
     .long-text-item p, .long-text-item span:not(.date-prefix) {
         color: var(--secondary-text-color);
     }

    .hidden {
      display: none;
    }

    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }
      .resume-container {
        box-shadow: none;
        margin: 0;
        width: 100%;
        min-height: 0;
      }
      .section {
        padding: 10px 20px;
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .basic-info-section {
         padding: 15px 20px;
      }
      .section-header {
        padding: 4px 10px;
        margin-bottom: 10px;
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .section-item, .long-text-item, .horizon-item-list li {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      .item-description p {
        orphans: 3;
        widows: 3;
      }
    }

    p, h1, h2, h3, span, li, div {
       word-wrap: break-word;
       overflow-wrap: break-word;
       max-width: 100%;
    }

    /* 确保所有Font Awesome图标正确显示 */
    .fa, .fas, .far, .fal, .fab {
      display: inline-block !important;
      font-style: normal !important;
      font-variant: normal !important;
      text-rendering: auto !important;
      line-height: 1 !important;
    }
  </style>
</head>
<body>
  <div class="resume-container">

    <header class="resume-header">
      <span class="resume-header-title">个人简历</span>
      <div class="resume-header-icons">
        <i class="fas fa-building" aria-hidden="true"></i>
        <i class="fas fa-graduation-cap" aria-hidden="true"></i>
        <i class="fas fa-seedling" aria-hidden="true"></i>
      </div>
    </header>

    
    <div class="section" id="name-header">
      <div class="basic-info-name">
          <h2>
              <span>张展伟</span>
          </h2>
      </div>
    </div>
    

    
    <section class="basic-info-section">
      <div class="basic-info-left">
        <div class="basic-info-details">
          <p><span>性别:</span><span>男</span></p>
          <p><span>年龄:</span><span>30</span></p>
          <p><span>电话:</span><span>18600009999</span></p>
          <p><span>邮箱:</span><span><EMAIL></span></p>
          <p><span>现居:</span><span>上海</span></p>
          
          <p><span>民族:</span><span>汉</span></p>
          <p><span>籍贯:</span><span>浙江</span></p>
          <p><span>政治面貌:</span><span>群众</span></p>
          <p><span>婚姻状况:</span><span>未婚</span></p>
          <p><span>出生日期:</span><span>2025-06-05</span></p>
          <p><span>身高:</span><span>180</span></p>
          <p><span>体重:</span><span>80</span></p>
          <p><span>微信:</span><span>zhanweir</span></p>
          <p><span>星座:</span><span>水瓶座</span></p>
          <p><span>血型:</span><span>A型</span></p>
        </div>
      </div>
      
      <img class="basic-info-photo" src="data:image/jpeg;base64,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" alt="个人照片" loading="eager"
           onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22110%22%20height%3D%22154%22%20viewBox%3D%220%200%20110%20154%22%3E%3Crect%20fill%3D%22%23e0e0e0%22%20width%3D%22110%22%20height%3D%22154%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2214%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2255%22%20y%3D%2277%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'; this.style.display='none';">
      
    </section>
    

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      
      <section class="section" id="education-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-graduation-cap" aria-hidden="true"></i>
            <h2>教育背景</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海交大</span>
              <span>会计学 (本科)</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>统计学, 数学, 英语, 市场营销</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海复旦大学</span>
              <span>新闻学 (硕士)</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>媒体传播, 组织学, 营销学</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

       
      <section class="section" id="school-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-school" aria-hidden="true"></i>
            <h2>在校经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
          <div class="item-header three-column"> 
            <span>学生会会长:</span>
            <span class="empty-span"></span>  
            <span class="date-range">2025-06 - 2025-06</span>
            <!-- <span class="date-prefix">2025-06</span> -->
            <!-- <span>在任职学生会会长期间, 我管理了1000人的社团, 组织外联, 喝酒聚会</span> -->
          </div>
            
            <div class="item-description">
              <p>在任职学生会会长期间, 我管理了1000人的社团, 组织外联, 喝酒聚会</p>
            </div>
            
          </div>
          
          <div class="section-item">
          <div class="item-header three-column"> 
            <span>篮球社队长:</span>
            <span class="empty-span"></span>  
            <span class="date-range">2025-06 - 2025-06</span>
            <!-- <span class="date-prefix">2025-06</span> -->
            <!-- <span>我篮球打的特别好, 在胜利拿过奖, 可以帮领导一起大球</span> -->
          </div>
            
            <div class="item-description">
              <p>我篮球打的特别好, 在胜利拿过奖, 可以帮领导一起大球</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      
      <section class="section" id="custom2-section">
        <div class="section-header">
            <div class="section-title">
                <i class="fas fa-star" aria-hidden="true"></i>
                <h2>名称二</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
            <div class="section-item">
               <div class="item-header two-column">
                 <span> 大幅</span>
                 <span class="date-range">2025-06 - 至今</span>
               </div>
               
                 <div class="item-description">
                    <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                 </div>
               
            </div>
          
        </div>
      </section>
      

      

    
      


      

      

      

      

      

      
      <section class="section" id="skills-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-certificate" aria-hidden="true"></i>
            <h2>技能证书</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            
            <li>技能1</li>
            
            <li>技能2</li>
            
            <li>技能3</li>
            
            <li>技能4</li>
            
            <li>技能5</li>
            
          </ul>
        </div>
      </section>
      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      
      <section class="section" id="evaluation-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-comment" aria-hidden="true"></i>
            <h2>自我评价</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
           
          <div class="item-description">
            <p>CSS align-items 属性设置了所有直接子元素的 align-self 值作为一个组。在 Flexbox 中，它控制子元素在交叉轴上的对齐。在 Grid 布局中，它控制了子元素在其网格区域内的块向轴上的对齐。

下面的交互示例演示了使用网格布局的 align-items 的一些值。</p> 
          </div>
          
        </div>
      </section>
      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      
      <section class="section" id="custom1-section">
        <div class="section-header">
            <div class="section-title">
                <i class="fas fa-star" aria-hidden="true"></i>
                <h2>规划</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
            <div class="section-item">
               <div class="item-header two-column">
                 <span> 队长</span>
                 <span class="date-range">2025-06 - 2025-06</span>
               </div>
               
                 <div class="item-description">
                    <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                 </div>
               
            </div>
          
        </div>
      </section>
      

      

      

    
      


      

      

      
      <section class="section" id="work-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-briefcase" aria-hidden="true"></i>
            <h2>工作经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海证券有限责任公司</span>
              <span>投资顾问</span>
              <span class="date-range">2025-06 - 至今</span>
            </div>
            
            <div class="item-description">
              <p>投资顾问分析, 基金分析, 上市公司调研
上市公司年报统计, 每周投资报告
组织部门活动, 高净值活动</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>上海宽辅私募基金合伙企业</span>
              <span>交易员职位</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理, 交易活动, 账户统计, 数据处理
每天都有很多交易任务</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      
      <section class="section" id="project-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-project-diagram" aria-hidden="true"></i>
            <h2>项目经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>ctp数据录取</span>
              <span>程序员</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>期货账户编写c++程序录制实时行情, level1 高频行情, 可以为交易分析 做基础</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>多空融券约券系统</span>
              <span>程序员技术员</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
            
            <div class="item-description">
              <p>根据也无需要, 对接券商空头约券系统, 实现实时约券查券
根据也无需要, 对接券商空头约券系统, 实现实时约券查券</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      
      <section class="section" id="awards-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-award" aria-hidden="true"></i>
            <h2>奖项荣誉</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          <ul class="horizon-item-list">
            
            <li>奖项1</li>
            
            <li>奖项2</li>
            
            <li>奖项3</li>
            
            <li>奖项4</li>
            
          </ul>
        </div>
      </section>
      

      

      

      

      

      

    
      
      
      <section class="section" id="jobIntention-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-bullseye" aria-hidden="true"></i>
            <h2>求职意向</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
            <div class="basic-info-details" style="grid-template-columns: repeat(2, 1fr); padding-left: 15px;">
                <p><span>期望职位:</span><span>总裁</span></p>
                <p><span>期望城市:</span><span>上海</span></p>
                <p><span>期望薪资:</span><span>3k以下</span></p>
                <p><span>求职状态:</span><span>目前在职</span></p>
            </div>
        </div>
      </section>
      


      

      

      

      

      

      

      

      

      

      

      

      

    
      


      

      
      <section class="section" id="internship-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fas fa-id-badge" aria-hidden="true"></i>
            <h2>实习经历</h2>
          </div>
          <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>阿里集团</span>
              <span>秘书</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
             
            <div class="item-description">
               <p>帮领导订饭店, 打酱油
参加部门会议</p>
            </div>
            
          </div>
          
          <div class="section-item">
            <div class="item-header three-column">
              <span>量化私募</span>
              <span>IT</span>
              <span class="date-range">2025-06 - 2025-06</span>
            </div>
             
            <div class="item-description">
               <p>抓数据, 洗数据, 做项目
啦啦啦</p>
            </div>
            
          </div>
          
        </div>
      </section>
      

      

      

      

      

      

      

      

      

      

      

    
      


      

      

      

      

      

      

      

      

      

      

      

      
      <section class="section" id="custom3-section">
        <div class="section-header">
            <div class="section-title">
                <i class="fas fa-star" aria-hidden="true"></i>
                <h2>人呢</h2>
            </div>
            <div class="section-header-line"></div>
        </div>
        <div class="section-content">
          
            <div class="section-item">
               <div class="item-header two-column">
                 <span> 角色</span>
                 <span class="date-range">2025-06 - 2025-06</span>
               </div>
               
                 <div class="item-description">
                    <p>习近平指出，校正中美关系这艘大船的航向，需要我们把好舵、定好向，尤其是排除各种干扰甚至破坏，这尤为重要。根据美方提议，两国经贸牵头人在日内瓦举行会谈，迈出了通过对话协商解决经贸问题的重要一步，受到两国各界和国际社会普遍欢迎，也证明对话和合作是唯一正确的选择。</p>
                 </div>
               
            </div>
          
        </div>
      </section>
      

    
      


      

      

      

      

      

      

      

      

      

      

      

      

    
  </div>
</body>
</html>