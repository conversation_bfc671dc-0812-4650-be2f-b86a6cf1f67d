#!/usr/bin/env python3
"""
免费简历模板数据导入脚本
从CSV文件导入数据到数据库
"""
import csv
import os
import sys
from datetime import datetime
from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.user import FreeTemplate

def import_templates_from_csv(csv_file_path: str):
    """
    从CSV文件导入免费模板数据
    
    Args:
        csv_file_path: CSV文件路径
    """
    if not os.path.exists(csv_file_path):
        print(f"错误：CSV文件不存在: {csv_file_path}")
        return False
    
    # 创建数据库会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        # 读取CSV文件
        with open(csv_file_path, 'r', encoding='gbk') as file:
            # 尝试检测CSV格式
            sample = file.read(1024)
            file.seek(0)
            
            # 检测分隔符
            delimiter = ','
            if '\t' in sample:
                delimiter = '\t'
            elif ';' in sample:
                delimiter = ';'
            
            csv_reader = csv.DictReader(file, delimiter=delimiter)
            
            print(f"检测到的CSV列名: {csv_reader.fieldnames}")
            
            imported_count = 0
            updated_count = 0
            
            for row_num, row in enumerate(csv_reader, start=2):  # 从第2行开始（第1行是标题）
                try:
                    # 处理数据，支持不同的列名格式
                    template_data = {}
                    
                    # 映射CSV列名到数据库字段名
                    field_mapping = {
                        'id': ['id', 'ID', '模板ID', 'template_id'],
                        'batch_flag': ['batch_flag', 'batchFlag', '批次', 'batch', '分类'],
                        'thumb_path': ['thumb_path', 'thumbPath', '缩略图路径', 'thumbnail', '图片路径'],
                        'baidu_url': ['baidu_url', 'baiduUrl', '百度链接', 'baidu_link', '百度网盘'],
                        'baidu_pass': ['baidu_pass', 'baiduPass', '百度提取码', 'baidu_password', '百度密码'],
                        'quark_url': ['quark_url', 'quarkUrl', '夸克链接', 'quark_link', '夸克网盘'],
                        'quark_pass': ['quark_pass', 'quarkPass', '夸克提取码', 'quark_password', '夸克密码'],
                        'download_count': ['download_count', 'downloadCount', '下载次数', 'downloads'],
                        'type': ['type', 'file_type', '文件类型', '类型']
                    }
                    
                    # 根据映射提取数据
                    for db_field, csv_fields in field_mapping.items():
                        for csv_field in csv_fields:
                            if csv_field in row and row[csv_field]:
                                template_data[db_field] = row[csv_field].strip()
                                break
                    
                    # 验证必需字段
                    if not template_data.get('id'):
                        print(f"警告：第{row_num}行缺少模板ID，跳过")
                        continue
                    
                    # 设置默认值
                    template_data.setdefault('batch_flag', template_data['id'].split('/')[0] if '/' in template_data['id'] else 'default')
                    template_data.setdefault('thumb_path', f"free_resume_templates/{template_data['batch_flag']}/{template_data['id']}")
                    template_data.setdefault('download_count', 0)
                    template_data.setdefault('type', 'word')
                    
                    # 转换数据类型
                    try:
                        template_data['download_count'] = int(template_data.get('download_count', 0))
                    except (ValueError, TypeError):
                        template_data['download_count'] = 0
                    
                    # 检查是否已存在
                    existing_template = session.query(FreeTemplate).filter(
                        FreeTemplate.id == template_data['id']
                    ).first()
                    
                    if existing_template:
                        # 更新现有记录
                        for key, value in template_data.items():
                            if key != 'id':  # 不更新主键
                                setattr(existing_template, key, value)
                        updated_count += 1
                        print(f"更新模板: {template_data['id']}")
                    else:
                        # 创建新记录
                        new_template = FreeTemplate(**template_data)
                        session.add(new_template)
                        imported_count += 1
                        print(f"导入模板: {template_data['id']}")
                
                except Exception as e:
                    print(f"错误：处理第{row_num}行时出错: {str(e)}")
                    print(f"行数据: {row}")
                    continue
            
            # 提交事务
            session.commit()
            
            print(f"\n导入完成！")
            print(f"新增模板: {imported_count} 个")
            print(f"更新模板: {updated_count} 个")
            print(f"总计处理: {imported_count + updated_count} 个模板")
            
            return True
            
    except Exception as e:
        session.rollback()
        print(f"导入失败: {str(e)}")
        return False
    finally:
        session.close()

def create_sample_csv():
    """创建示例CSV文件"""
    sample_data = [
        {
            'id': 'blackWhite/10.jpg',
            'batch_flag': 'blackWhite',
            'thumb_path': 'free_resume_templates/blackWhite/10.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567890',
            'baidu_pass': '00aa',
            'quark_url': 'https://sxsdlfkjslfjsdf',
            'quark_pass': '00aa',
            'download_count': '0',
            'type': 'word'
        },
        {
            'id': 'blackWhite/11.jpg',
            'batch_flag': 'blackWhite',
            'thumb_path': 'free_resume_templates/blackWhite/11.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567891',
            'baidu_pass': '00bb',
            'quark_url': 'https://sxsdlfkjslfjsdf1',
            'quark_pass': '00bb',
            'download_count': '5',
            'type': 'word'
        },
        {
            'id': 'colorful/01.jpg',
            'batch_flag': 'colorful',
            'thumb_path': 'free_resume_templates/colorful/01.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567892',
            'baidu_pass': '00cc',
            'quark_url': 'https://sxsdlfkjslfjsdf2',
            'quark_pass': '00cc',
            'download_count': '12',
            'type': 'word'
        }
    ]
    
    csv_file = 'sample_free_templates.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as file:
        fieldnames = ['id', 'batch_flag', 'thumb_path', 'baidu_url', 'baidu_pass', 'quark_url', 'quark_pass', 'download_count', 'type']
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(sample_data)
    
    print(f"示例CSV文件已创建: {csv_file}")
    return csv_file

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python import_free_templates.py <csv_file_path>  # 从CSV文件导入")
        print("  python import_free_templates.py --sample        # 创建示例CSV文件")
        print("  python import_free_templates.py --help          # 显示帮助")
        return
    
    if sys.argv[1] == '--sample':
        csv_file = create_sample_csv()
        print(f"\n可以使用以下命令导入示例数据:")
        print(f"python import_free_templates.py {csv_file}")
    elif sys.argv[1] == '--help':
        print("免费简历模板数据导入工具")
        print("\n支持的CSV列名格式:")
        print("- id: 模板ID")
        print("- batch_flag: 批次标识")
        print("- thumb_path: 缩略图路径")
        print("- baidu_url: 百度网盘链接")
        print("- baidu_pass: 百度网盘提取码")
        print("- quark_url: 夸克网盘链接")
        print("- quark_pass: 夸克网盘提取码")
        print("- download_count: 下载次数")
        print("- type: 文件类型")
    else:
        csv_file_path = sys.argv[1]
        print(f"开始从CSV文件导入数据: {csv_file_path}")
        success = import_templates_from_csv(csv_file_path)
        if success:
            print("导入成功！")
        else:
            print("导入失败！")
            sys.exit(1)

if __name__ == "__main__":
    main()
